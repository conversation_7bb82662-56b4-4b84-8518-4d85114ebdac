#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分批JIT签署功能
功能：测试修改后的分批JIT签署逻辑是否正确
"""

import sys
import os

# 添加实践代码目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '实践代码'))

def test_batch_logic():
    """
    测试分批逻辑
    """
    print("🧪 测试分批逻辑")
    
    # 模拟不同数量的商品分批情况
    test_cases = [
        (5, 1),    # 5个商品 -> 1批
        (20, 1),   # 20个商品 -> 1批
        (25, 2),   # 25个商品 -> 2批
        (40, 2),   # 40个商品 -> 2批
        (50, 3),   # 50个商品 -> 3批
        (100, 5),  # 100个商品 -> 5批
    ]
    
    batch_size = 20
    
    for total_products, expected_batches in test_cases:
        calculated_batches = (total_products + batch_size - 1) // batch_size
        
        if calculated_batches == expected_batches:
            print(f"   ✅ {total_products}个商品 -> {calculated_batches}批 (正确)")
        else:
            print(f"   ❌ {total_products}个商品 -> {calculated_batches}批 (期望{expected_batches}批)")
            return False
    
    print("✅ 分批逻辑测试通过")
    return True

def test_batch_splitting():
    """
    测试商品分批切分
    """
    print("\n🧪 测试商品分批切分")
    
    # 模拟商品列表
    products = []
    for i in range(1, 55):  # 创建54个商品
        products.append({
            'productId': f'product_{i}',
            'productSkcId': f'skc_{i}'
        })
    
    batch_size = 20
    batches = []
    
    # 分批处理
    for i in range(0, len(products), batch_size):
        batch = products[i:i + batch_size]
        batches.append(batch)
    
    print(f"   📊 总商品数: {len(products)}")
    print(f"   📊 分批数量: {len(batches)}")
    print(f"   📊 各批次大小: {[len(batch) for batch in batches]}")
    
    # 验证分批结果
    expected_batches = 3  # 54个商品应该分成3批：20+20+14
    if len(batches) == expected_batches:
        if len(batches[0]) == 20 and len(batches[1]) == 20 and len(batches[2]) == 14:
            print("✅ 商品分批切分测试通过")
            return True
    
    print("❌ 商品分批切分测试失败")
    return False

def test_import_new_methods():
    """
    测试新增方法的导入
    """
    print("\n🧪 测试新增方法的导入")
    
    try:
        from 商品参数提取器_批量版 import ProductExtractorGUI
        
        # 检查是否有新增的方法
        if hasattr(ProductExtractorGUI, 'sign_single_batch'):
            print("   ✅ sign_single_batch方法存在")
        else:
            print("   ❌ sign_single_batch方法不存在")
            return False
            
        if hasattr(ProductExtractorGUI, 'batch_sign_jit_agreement'):
            print("   ✅ batch_sign_jit_agreement方法存在")
        else:
            print("   ❌ batch_sign_jit_agreement方法不存在")
            return False
        
        print("✅ 新增方法导入测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 新增方法导入测试失败：{e}")
        return False

def simulate_batch_processing():
    """
    模拟分批处理过程
    """
    print("\n🧪 模拟分批处理过程")
    
    # 模拟100个需要签署的商品
    products_to_sign = []
    for i in range(1, 101):
        products_to_sign.append({
            'productId': f'product_{i:04d}',
            'productSkcId': f'skc_{i:04d}'
        })
    
    batch_size = 20
    total_batches = (len(products_to_sign) + batch_size - 1) // batch_size
    
    print(f"   📊 模拟签署{len(products_to_sign)}个商品")
    print(f"   📊 每批{batch_size}个，共{total_batches}批")
    
    # 模拟分批处理
    for i in range(0, len(products_to_sign), batch_size):
        batch_products = products_to_sign[i:i + batch_size]
        batch_num = (i // batch_size) + 1
        
        print(f"   📦 第{batch_num}/{total_batches}批: {len(batch_products)}个商品")
        print(f"      商品ID范围: {batch_products[0]['productId']} ~ {batch_products[-1]['productId']}")
    
    print("✅ 分批处理过程模拟完成")
    return True

def main():
    """
    主测试函数
    """
    print("🧪 开始测试分批JIT签署功能")
    print("=" * 60)
    
    # 测试项目列表
    tests = [
        ("分批逻辑测试", test_batch_logic),
        ("商品分批切分测试", test_batch_splitting),
        ("新增方法导入测试", test_import_new_methods),
        ("分批处理过程模拟", simulate_batch_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    # 执行所有测试
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name}失败")
        except Exception as e:
            print(f"❌ {test_name}异常：{e}")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    print(f"✅ 通过测试：{passed}/{total}")
    print(f"❌ 失败测试：{total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！分批JIT签署功能修改成功")
        print("💡 主要改进：")
        print("   - 每批最多签署20个商品，避免请求过大")
        print("   - 增加详细的响应信息打印")
        print("   - 批次间添加延迟，避免请求过快")
        print("   - 更好的错误处理和日志记录")
    else:
        print("⚠️ 部分测试失败，请检查代码修改")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
